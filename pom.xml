<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>io.searchbox</groupId>
    <artifactId>jest-parent</artifactId>
    <packaging>pom</packaging>
    <version>6.3.2-SNAPSHOT</version>
    <name>Jest Parent POM</name>
    <description>Parent POM for Jest - ElasticSearch Java rest client</description>
    <url>https://github.com/searchbox-io/Jest</url>

    <modules>
        <module>jest-common</module>
        <module>jest</module>
        <module>jest-droid</module>
    </modules>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution
        </license>
    </licenses>

    <scm>
        <connection>scm:git:**************:searchbox-io/Jest.git</connection>
        <developerConnection>scm:git:**************:searchbox-io/Jest.git</developerConnection>
        <url>**************:searchbox-io/Jest.git</url>
    </scm>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <developers>
        <developer>
            <id>TheKingOfTheEast</id>
            <name>Dogukan Sonmez</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>ferhatsb</id>
            <name>Ferhat Sobay</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>kramer</id>
            <name>Cihat Keser</name>
            <email><EMAIL></email>
            <url>http://about.cihatkeser.com</url>
        </developer>
    </developers>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <elasticsearch.version>6.8.10</elasticsearch.version>

        <lucene.version>7.7.3</lucene.version>
        <hamcrest.version>1.3</hamcrest.version>
        <randomizedtesting.version>2.4.0</randomizedtesting.version>
        <mustache.version>0.9.4</mustache.version>

        <httpcore.version>4.4.6</httpcore.version>
        <httpclient.version>4.5.3</httpclient.version>
        <httpAsyncClient.version>4.1.3</httpAsyncClient.version>
        <httpclientandroid.version>4.3.5.1</httpclientandroid.version>

        <slf4j.version>1.7.25</slf4j.version>
        <log4j.version>2.7</log4j.version>

        <guava.version>21.0</guava.version>
        <gson.version>2.8.0</gson.version>
        <mockito.version>1.10.19</mockito.version>

        <commons-io.version>2.5</commons-io.version>
        <junit.version>4.12</junit.version>
        <jsonassert.version>1.5.0</jsonassert.version>

        <plugin.surefire.version>2.19.1</plugin.surefire.version>
        <plugin.sonatype.version>1.6.7</plugin.sonatype.version>
        <plugin.gpg.version>1.6</plugin.gpg.version>
    </properties>
    <build>
        <defaultGoal>install</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>${plugin.sonatype.version}</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${plugin.surefire.version}</version>
                <configuration>
                    <excludes>
                        <exclude>**/*WebTest.java</exclude>
                    </excludes>
                    <useFile>false</useFile>
                    <argLine>-Djava.net.preferIPv4Stack=true -Xmx1024m -XX:MaxPermSize=256m</argLine>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.3</version>
                <configuration>
                    <failOnError>false</failOnError>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <format>xml</format>
                    <maxmem>256m</maxmem>
                    <!-- aggregated reports for multi-module projects -->
                    <aggregate>true</aggregate>
                    <check />
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>4.0.0</version>
            </plugin>

        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
    </build>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!-- Http components -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>${httpAsyncClient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore-nio</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.searchbox</groupId>
                <artifactId>jest-common</artifactId>
                <version>6.3.2-SNAPSHOT</version>
            </dependency>

            <!-- Testing Dependencies -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${jsonassert.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>

            <!--<dependency>-->
                <!--<groupId>org.elasticsearch.plugin</groupId>-->
                <!--<artifactId>delete-by-query</artifactId>-->
                <!--<version>${elasticsearch.version}</version>-->
                <!--<scope>test</scope>-->
            <!--</dependency>-->
            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>reindex-client</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>transport-netty4-client</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.codelibs.elasticsearch.module</groupId>
                <artifactId>lang-painless</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>percolator-client</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>lang-mustache-client</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.module</groupId>
                <artifactId>analysis-common</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>hamcrest-core</artifactId>
                        <groupId>org.hamcrest</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- required by elasticsearch-test-jar -->
            <dependency>
                <groupId>org.elasticsearch.test</groupId>
                <artifactId>framework</artifactId>
                <version>${elasticsearch.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-test-framework</artifactId>
                <version>${lucene.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>hamcrest-core</artifactId>
                        <groupId>org.hamcrest</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.carrotsearch.randomizedtesting</groupId>
                <artifactId>randomizedtesting-runner</artifactId>
                <version>${randomizedtesting.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-codecs</artifactId>
                <version>${lucene.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.littleshoot</groupId>
                <artifactId>littleproxy</artifactId>
                <version>1.1.2</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        
            <dependency>
                <groupId>com.github.spullara.mustache.java</groupId>
                <artifactId>compiler</artifactId>
                <version>${mustache.version}</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>integration-test</id>
            <activation>
                <property>
                    <name>!skipTests</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${plugin.surefire.version}</version>
                        <executions>
                            <execution>
                                <id>surefire-it</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <excludes>
                                        <exclude>none</exclude>
                                    </excludes>
                                    <argLine>-Djava.net.preferIPv4Stack=true -Dtests.security.manager=false
                                        -Des.set.netty.runtime.available.processors=false
                                        -Des.set.netty.runtime.available.processors=false
                                    </argLine>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <argLine>-Dtests.security.manager=false -Des.set.netty.runtime.available.processors=false
                                -Des.set.netty.runtime.available.processors=false
                            </argLine>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${plugin.gpg.version}</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>travis</id>
            <activation>
                <property>
                    <name>env.TRAVIS</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <tests.heap.size>1g</tests.heap.size>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>surefire-it</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <excludes>
                                        <exclude>none</exclude>
                                    </excludes>
                                    <argLine>-Xmx${tests.heap.size} -Xms${tests.heap.size}
                                        -Dtests.heap.size=${tests.heap.size} -Djava.net.preferIPv4Stack=true
                                        -Dtests.security.manager=false
                                        -Des.set.netty.runtime.available.processors=false
                                    </argLine>
                                    <reuseForks>false</reuseForks>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <argLine>-Xmx${tests.heap.size} -Xms${tests.heap.size} -Dtests.heap.size=${tests.heap.size}
                                -Dtests.security.manager=false
                                -Des.set.netty.runtime.available.processors=false
                            </argLine>
                            <reuseForks>false</reuseForks>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
